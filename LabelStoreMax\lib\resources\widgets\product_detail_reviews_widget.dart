//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '/resources/pages/product_reviews_page.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/product_detail_review_tile_widget.dart';
import '/app/services/woocommerce_service.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/app/models/app_config.dart';

class ProductDetailReviewsWidget extends StatefulWidget {
  const ProductDetailReviewsWidget(
      {super.key, required this.product, required this.appConfig});
  final MyProduct? product;
  final AppConfig? appConfig;

  @override
  createState() => _ProductDetailReviewsWidgetState();
}

class _ProductDetailReviewsWidgetState
    extends State<ProductDetailReviewsWidget> {
  bool _ratingExpanded = false;

  @override
  Widget build(BuildContext context) {
    // Note: MyProduct doesn't have reviewsAllowed field, so we check appConfig only
    if (widget.appConfig?.showProductReviews == false) {
      return SizedBox.shrink();
    }

    return Row(
      children: <Widget>[
        Expanded(
            child: ExpansionTile(
          textColor: ThemeColor.get(context).primaryAccent,
          iconColor: ThemeColor.get(context).primaryAccent,
          tilePadding: EdgeInsets.symmetric(horizontal: 16),
          childrenPadding: EdgeInsets.all(0),
          title: AutoSizeText(
            "${trans("Reviews")}",
            maxLines: 1,
          ),
          onExpansionChanged: (value) {
            setState(() {
              _ratingExpanded = value;
            });
          },
          trailing: SizedBox(
            width: MediaQuery.of(context).size.width / 2,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                RatingBarIndicator(
                  rating: 0.0, // MyProduct doesn't have averageRating, will be calculated from reviews
                  itemBuilder: (context, index) => Icon(
                    Icons.star,
                    color: Colors.amber,
                  ),
                  itemCount: 5,
                  itemSize: 25.0,
                  direction: Axis.horizontal,
                ),
                Icon(
                    _ratingExpanded
                        ? Icons.keyboard_arrow_down_rounded
                        : Icons.keyboard_arrow_up_rounded,
                    size: 30)
              ],
            ),
          ),
          initiallyExpanded: false,
          children: [
            if (_ratingExpanded == true)
              NyFutureBuilder<List<Map<String, dynamic>>>(
                future: fetchReviews(),
                child: (context, reviews) {
                  if (reviews == null) {
                    return ListTile(
                      title: Text(
                        trans('There are no reviews yet.'),
                      ),
                    );
                  }
                  int reviewsCount = reviews.length;
                  List<Widget> childrenWidgets = [];
                  List<ProductDetailReviewTileWidget> children = reviews
                      .map((review) =>
                          ProductDetailReviewTileWidget(productReview: review))
                      .toList();
                  childrenWidgets.addAll(children);

                  if (reviewsCount >= 5) {
                    childrenWidgets.add(
                      ListTile(
                          contentPadding: EdgeInsets.symmetric(horizontal: 16),
                          title: Text(
                            trans('See More Reviews'),
                          ),
                          onTap: () => routeTo(ProductReviewsPage.path,
                              data: widget.product)),
                    );
                  }
                  return ListView(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.all(0),
                    children: reviews.isEmpty
                        ? [
                            ListTile(
                              title: Text(
                                trans('There are no reviews yet.'),
                              ),
                            )
                          ]
                        : childrenWidgets,
                  );
                },
                loadingStyle: LoadingStyle.normal(
                    child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: CupertinoActivityIndicator(),
                )),
              ),
          ],
        )),
      ],
    );
  }

  Future<List<Map<String, dynamic>>> fetchReviews() async {
    if (widget.product?.id == null) return [];

    try {
      print('📝 Fetching reviews for product ${widget.product!.id}...');
      print('📝 Product type: ${widget.product!.type}');
      print('📝 Product name: ${widget.product!.name}');

      final wooCommerceService = WooCommerceService();
      List<WooProductReview> reviews = await wooCommerceService.getProductReviews(
        perPage: 5,
        product: [widget.product!.id],
      );

      // Filter for approved reviews only
      List<WooProductReview> approvedReviews = reviews
          .where((review) => review.status == WooProductReviewStatus.approved)
          .toList();

      print('✅ Found ${approvedReviews.length} approved reviews');

      // Convert WooProductReview objects to Map<String, dynamic> format
      // that the existing UI components expect
      List<Map<String, dynamic>> reviewMaps = approvedReviews.map((review) {
        return {
          'id': review.id,
          'review': review.review ?? '',
          'rating': review.rating ?? 0,
          'reviewer': review.reviewer ?? 'Anonymous',
          'reviewer_email': review.reviewerEmail ?? '',
          'date_created': review.dateCreated?.toIso8601String() ?? '',
          'verified': review.verified ?? false,
          'status': review.status?.name ?? 'approved',
        };
      }).toList();

      return reviewMaps;

    } catch (e) {
      print('❌ Error fetching product reviews: $e');
      print('❌ Error type: ${e.runtimeType}');
      print('❌ Error details: ${e.toString()}');

      // Check if it's a 400 error specifically
      if (e.toString().contains('400') || e.toString().contains('bad response')) {
        print('❌ 400 Bad Request detected - this might indicate:');
        print('   1. Reviews are not enabled for this WooCommerce store');
        print('   2. The product ID is invalid');
        print('   3. The reviews endpoint requires different parameters');
        print('   4. Authentication issues with the reviews endpoint');

        // Try a fallback approach - fetch all reviews without product filter
        try {
          print('🔄 Attempting fallback: fetching all reviews...');
          final wooCommerceService = WooCommerceService();
          List<WooProductReview> allReviews = await wooCommerceService.getProductReviews(
            perPage: 50, // Get more reviews to filter locally
            // No product filter - get all reviews
          );

          // Filter locally for this product
          List<WooProductReview> productReviews = allReviews
              .where((review) => review.productId == widget.product!.id)
              .where((review) => review.status == WooProductReviewStatus.approved)
              .toList();

          print('✅ Fallback successful: Found ${productReviews.length} reviews for product ${widget.product!.id}');

          // Convert to expected format
          List<Map<String, dynamic>> reviewMaps = productReviews.map((review) {
            return {
              'id': review.id,
              'review': review.review ?? '',
              'rating': review.rating ?? 0,
              'reviewer': review.reviewer ?? 'Anonymous',
              'reviewer_email': review.reviewerEmail ?? '',
              'date_created': review.dateCreated?.toIso8601String() ?? '',
              'verified': review.verified ?? false,
              'status': review.status?.name ?? 'approved',
            };
          }).toList();

          return reviewMaps;

        } catch (fallbackError) {
          print('❌ Fallback also failed: $fallbackError');
          print('💡 Reviews functionality may not be available for this store');
        }
      }

      // Return empty list instead of null to avoid UI issues
      return <Map<String, dynamic>>[];
    }
  }
}
