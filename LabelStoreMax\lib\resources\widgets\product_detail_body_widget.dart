//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/pages/product_image_viewer_page.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/resources/widgets/product_detail_description_widget.dart';
import '/resources/widgets/product_detail_header_widget.dart';
import '/resources/widgets/product_detail_image_swiper_widget.dart';
// Re-enabled imports after WooCommerce migration
import '/resources/widgets/product_detail_related_products_widget.dart';
import '/resources/widgets/product_detail_reviews_widget.dart';


import '/app/models/woocommerce_wrappers/my_product.dart';
import '/bootstrap/app_helper.dart';

class ProductDetailBodyWidget extends StatelessWidget {
  const ProductDetailBodyWidget(
      {super.key, required this.product});

  final MyProduct? product;

  @override
  Widget build(BuildContext context) {
    return ListView(
      shrinkWrap: true,
      children: <Widget>[
        ProductDetailImageSwiperWidget(
            product: product,
            onTapImage: (i) => _viewProductImages(context, i)),
        // </Image Swiper>

        ProductDetailHeaderWidget(product: product),
        // </Header title + price>

        ProductDetailDescriptionWidget(product: product),
        // </Description body>

        // Product Reviews - migrated to WooCommerce
        if (AppHelper.instance.appConfig?.showProductReviews == true)
          ProductDetailReviewsWidget(
            product: product,
            appConfig: AppHelper.instance.appConfig,
          ),
        // </Product reviews>

        // Upsell Products - Note: MyProduct doesn't have upsellIds, so we skip this for now
        // if (product != null && AppHelper.instance.appConfig?.showUpsellProducts == true)
        //   ProductDetailUpsellWidget(
        //     productIds: [], // Empty list since MyProduct doesn't have upsellIds
        //     appConfig: AppHelper.instance.appConfig,
        //   ),
        // </You may also like>

        // Related Products - migrated to WooCommerce
        if (AppHelper.instance.appConfig?.showRelatedProducts == true)
          ProductDetailRelatedProductsWidget(
            product: product,
            appConfig: AppHelper.instance.appConfig,
          ),
        // </Related products>
      ],
    );
  }

  _viewProductImages(BuildContext context, int i) {
    routeTo(ProductImageViewerPage.path, data: {
      "index": i,
      "images": product?.images.map((f) => f.getSafeImageSrc()).toList()
    });
  }
}
