//  StoreMob
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/safearea_widget.dart';
import '/app/services/woocommerce_service.dart';

import 'package:nylo_framework/nylo_framework.dart';
// import 'package:woosignal/models/response/woosignal_app.dart'; // Replaced with AppConfig

class NoConnectionPage extends NyStatefulWidget {
  static RouteView path = ("/no-connection", (_) => NoConnectionPage());

  NoConnectionPage({super.key}) : super(child: () => _NoConnectionPageState());
}

class _NoConnectionPageState extends NyPage<NoConnectionPage> {
  _NoConnectionPageState();

  @override
  get init => () {
        if (getEnv('APP_DEBUG') == true) {
          NyLogger.error('WooCommerce site is not connected');
        }
      };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      body: SafeAreaWidget(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Icon(
                Icons.error_outline,
                size: 100,
                color: Colors.black54,
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  trans("Oops, something went wrong"),
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ),
              LinkButton(title: trans("Retry"), action: _retry),
            ],
          ),
        ),
      ),
    );
  }

  _retry() async {
    try {
      print('🔄 Attempting to reconnect to WooCommerce API...');

      // Show loading indicator
      showToast(
        title: trans("Connecting"),
        description: trans("Testing connection to WooCommerce..."),
        icon: Icons.refresh,
        style: ToastNotificationStyleType.info,
      );

      // Test WooCommerce API connection by fetching basic store info
      final wooCommerceService = WooCommerceService();

      // Try to fetch a simple endpoint to test connectivity
      await wooCommerceService.getProducts(page: 1, perPage: 1);

      print('✅ WooCommerce API connection successful');

      // Connection successful - show success message and navigate back
      showToast(
        title: trans("Success"),
        description: trans("Connection restored successfully"),
        icon: Icons.check,
        style: ToastNotificationStyleType.success,
      );

      // Navigate back to the previous screen
      Navigator.of(context).pop();

      // Optionally, navigate to initial route if needed
      // routeToInitial();

    } catch (e) {
      print('❌ WooCommerce API connection failed: $e');

      // Connection failed - show error message
      showToast(
        title: trans("Connection Failed"),
        description: trans("Unable to connect to the store. Please check your internet connection and try again."),
        icon: Icons.error_outline,
        style: ToastNotificationStyleType.danger,
      );
    }
  }
}
