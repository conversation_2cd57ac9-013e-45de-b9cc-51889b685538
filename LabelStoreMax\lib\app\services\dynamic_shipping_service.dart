//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.


import '/app/services/woocommerce_service.dart';

/// Service to dynamically fetch and manage shipping methods from WooCommerce
class DynamicShippingService {
  static final DynamicShippingService _instance = DynamicShippingService._internal();
  factory DynamicShippingService() => _instance;
  DynamicShippingService._internal();

  final WooCommerceService _wooService = WooCommerceService();
  List<ShippingZone>? _cachedShippingZones;
  List<ShippingMethod>? _cachedShippingMethods;

  /// Fetch shipping zones from WooCommerce API
  Future<List<ShippingZone>> getShippingZones({bool forceRefresh = false}) async {
    print('=== Dynamic Shipping Zones Fetch ===');
    
    // Return cached data if available and not forcing refresh
    if (_cachedShippingZones != null && !forceRefresh) {
      print('✅ Returning cached shipping zones: ${_cachedShippingZones!.length}');
      return _cachedShippingZones!;
    }

    try {
      // Fetch shipping zones from WooCommerce API
      final response = await _wooService.wooCommerce.dio.get('/shipping/zones');
      
      print('✅ Shipping Zones API Success');
      print('Total zones found: ${response.data.length}');
      
      List<ShippingZone> shippingZones = [];
      
      for (var zone in response.data) {
        ShippingZone shippingZone = ShippingZone.fromWooZone(zone);
        shippingZones.add(shippingZone);
        
        print('✅ Added shipping zone: ${shippingZone.name} (ID: ${shippingZone.id})');
      }
      
      // Cache the results
      _cachedShippingZones = shippingZones;
      
      print('✅ Dynamic shipping zones loaded: ${shippingZones.length} zones');
      return shippingZones;
      
    } catch (e) {
      print('❌ Failed to fetch shipping zones: $e');
      return [];
    }
  }

  /// Fetch shipping methods for all zones from WooCommerce API
  Future<List<ShippingMethod>> getAllShippingMethods({bool forceRefresh = false}) async {
    print('=== Dynamic Shipping Methods Fetch ===');
    
    // Return cached data if available and not forcing refresh
    if (_cachedShippingMethods != null && !forceRefresh) {
      print('✅ Returning cached shipping methods: ${_cachedShippingMethods!.length}');
      return _cachedShippingMethods!;
    }

    try {
      // First get all zones
      final zones = await getShippingZones(forceRefresh: forceRefresh);
      
      List<ShippingMethod> allMethods = [];
      
      // For each zone, get its methods
      for (var zone in zones) {
        try {
          final methodsResponse = await _wooService.wooCommerce.dio.get('/shipping/zones/${zone.id}/methods');
          
          print('✅ Zone ${zone.id} (${zone.name}) has ${methodsResponse.data.length} methods');
          
          for (var method in methodsResponse.data) {
            ShippingMethod shippingMethod = ShippingMethod.fromWooMethod(method, zone);
            allMethods.add(shippingMethod);
            
            print('✅ Added shipping method: ${shippingMethod.title} (${shippingMethod.methodId})');
          }
        } catch (e) {
          print('❌ Failed to get methods for zone ${zone.id}: $e');
        }
      }
      
      // Cache the results
      _cachedShippingMethods = allMethods;
      
      print('✅ Dynamic shipping methods loaded: ${allMethods.length} methods');
      return allMethods;
      
    } catch (e) {
      print('❌ Failed to fetch shipping methods: $e');
      return [];
    }
  }

  /// Get shipping methods for a specific location
  Future<List<ShippingMethod>> getShippingMethodsForLocation({
    String? country,
    String? state,
    String? city,
    bool forceRefresh = false,
  }) async {
    print('=== Getting Shipping Methods for Location ===');
    print('Country: $country, State: $state, City: $city');

    final allMethods = await getAllShippingMethods(forceRefresh: forceRefresh);

    // Implement proper zone location matching based on WooCommerce zone rules
    List<ShippingMethod> filteredMethods = [];

    for (var method in allMethods) {
      bool methodMatches = await _doesMethodMatchLocation(
        method,
        country: country,
        state: state,
        city: city
      );

      if (methodMatches) {
        filteredMethods.add(method);
        print('✅ Method "${method.title}" matches location');
      } else {
        print('❌ Method "${method.title}" does not match location');
      }
    }

    // If no methods match, return all methods as fallback
    if (filteredMethods.isEmpty) {
      print('⚠️ No methods matched location, returning all methods as fallback');
      return allMethods;
    }

    print('✅ Returning ${filteredMethods.length} shipping methods for location');
    return filteredMethods;
  }

  /// Check if a shipping method matches the given location
  Future<bool> _doesMethodMatchLocation(
    ShippingMethod method, {
    String? country,
    String? state,
    String? city,
  }) async {
    try {
      // Get zone locations from WooCommerce API
      final response = await _wooService.wooCommerce.dio.get('/shipping/zones/${method.zone.id}/locations');

      print('Zone ${method.zone.id} locations: ${response.data}');

      // If no locations are defined, zone applies to all locations
      if (response.data.isEmpty) {
        print('Zone ${method.zone.id} has no location restrictions (applies everywhere)');
        return true;
      }

      // Check if the customer location matches any zone location
      for (var location in response.data) {
        String locationType = location['type'] ?? '';
        String locationCode = location['code'] ?? '';

        // Handle different location types
        switch (locationType) {
          case 'country':
            // For Libya, check if country matches
            if (country != null && (
                country.toLowerCase().contains('libya') ||
                country.toLowerCase().contains('ليبيا') ||
                locationCode.toUpperCase() == 'LY'
            )) {
              print('✅ Country match: $country matches zone location $locationCode');
              return true;
            }
            break;
          case 'state':
            // Check state/region match
            if (state != null && locationCode.contains(state)) {
              print('✅ State match: $state matches zone location $locationCode');
              return true;
            }
            break;
          case 'postcode':
            // For Libyan cities, we can match by city name patterns
            if (city != null && locationCode.contains(city)) {
              print('✅ Postcode/City match: $city matches zone location $locationCode');
              return true;
            }
            break;
        }
      }

      print('❌ No location match found for zone ${method.zone.id}');
      return false;

    } catch (e) {
      print('❌ Error checking zone locations for zone ${method.zone.id}: $e');
      // If we can't check locations, assume method applies (fallback)
      return true;
    }
  }

  /// Clear cached shipping data
  void clearCache() {
    _cachedShippingZones = null;
    _cachedShippingMethods = null;
    print('🗑️ Shipping cache cleared');
  }

  /// Get a specific shipping method by ID
  Future<ShippingMethod?> getShippingMethod(String methodId) async {
    final methods = await getAllShippingMethods();
    try {
      return methods.firstWhere((method) => method.methodId == methodId);
    } catch (e) {
      print('❌ Shipping method not found: $methodId');
      return null;
    }
  }

  /// Check if a specific shipping method is available
  Future<bool> isShippingMethodAvailable(String methodId) async {
    final method = await getShippingMethod(methodId);
    return method != null && method.enabled;
  }
}

/// Shipping Zone model
class ShippingZone {
  final int id;
  final String name;
  final int order;

  ShippingZone({
    required this.id,
    required this.name,
    required this.order,
  });

  factory ShippingZone.fromWooZone(Map<String, dynamic> zone) {
    return ShippingZone(
      id: zone['id'] ?? 0,
      name: zone['name'] ?? 'Unknown Zone',
      order: zone['order'] ?? 0,
    );
  }
}

/// Shipping Method model
class ShippingMethod {
  final int instanceId;
  final String title;
  final int order;
  final bool enabled;
  final String methodId;
  final String methodTitle;
  final String methodDescription;
  final Map<String, dynamic> settings;
  final ShippingZone zone;

  ShippingMethod({
    required this.instanceId,
    required this.title,
    required this.order,
    required this.enabled,
    required this.methodId,
    required this.methodTitle,
    required this.methodDescription,
    required this.settings,
    required this.zone,
  });

  factory ShippingMethod.fromWooMethod(Map<String, dynamic> method, ShippingZone zone) {
    return ShippingMethod(
      instanceId: method['instance_id'] ?? 0,
      title: method['title'] ?? method['method_title'] ?? 'Unknown Method',
      order: method['order'] ?? 0,
      enabled: method['enabled'] ?? false,
      methodId: method['method_id'] ?? '',
      methodTitle: method['method_title'] ?? '',
      methodDescription: method['method_description'] ?? '',
      settings: method['settings'] ?? {},
      zone: zone,
    );
  }

  /// Get the cost of this shipping method
  String get cost {
    print('🔍 ===== SHIPPING METHOD COST ANALYSIS =====');
    print('🔍 Method: $title');
    print('🔍 Method ID: $methodId');
    print('🔍 Full settings: $settings');
    print('🔍 settings[\'cost\']: ${settings['cost']}');
    print('🔍 settings keys: ${settings.keys.toList()}');

    if (settings['cost'] != null) {
      print('🔍 Cost field exists: ${settings['cost']}');
      String result = settings['cost']['value'] ?? '0';
      print('🔍 Extracted cost value: $result');
      return result;
    }

    print('🔍 No cost field found, returning 0');
    print('🔍 ==========================================');
    return '0';
  }

  /// Get the minimum amount for this shipping method
  String? get minimumAmount {
    if (settings['min_amount'] != null) {
      return settings['min_amount']['value'];
    }
    return null;
  }
}
