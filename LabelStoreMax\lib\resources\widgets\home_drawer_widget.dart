//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/services/auth_service.dart';
import '/resources/widgets/store_logo_widget.dart';
import '/resources/pages/account_detail_page.dart';
import '/resources/pages/account_login_page.dart';
import '/resources/pages/cart_page.dart';
import '/resources/pages/wishlist_page_widget.dart';
import '/resources/pages/browse_category_page.dart';

import '/app/models/woocommerce_wrappers/my_product_category.dart';
import '/app/models/app_config.dart';
import '/bootstrap/app_helper.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/app_version_widget.dart';

import 'package:nylo_framework/theme/helper/ny_theme.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:url_launcher/url_launcher.dart';


class HomeDrawerWidget extends StatefulWidget {
  const HomeDrawerWidget(
      {super.key,
      this.productCategories = const []});

  final List<MyProductCategory> productCategories;

  @override
  createState() => _HomeDrawerWidgetState();
}

class _HomeDrawerWidgetState extends State<HomeDrawerWidget> {
  List<MenuLink> _menuLinks = []; // Menu links from AppConfig
  String? _themeType;

  @override
  void initState() {
    super.initState();
    // Load menu links from AppConfig
    _menuLinks = AppHelper.instance.appConfig?.menuLinks ?? [];
    _themeType = AppHelper.instance.appConfig!.theme;
  }

  @override
  Widget build(BuildContext context) {
    bool isDark = (Theme.of(context).brightness == Brightness.dark);
    return Drawer(
      child: Container(
        color: ThemeColor.get(context).background,
        child: ListView(
          padding: EdgeInsets.zero,
          children: <Widget>[
            DrawerHeader(
              decoration: BoxDecoration(
                color: ThemeColor.get(context).background,
              ),
              child: Center(child: StoreLogo()),
            ),
            if (["compo"].contains(_themeType) == false)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 16, top: 8, bottom: 8),
                    child: Text(
                      trans("Menu"),
                      style: Theme.of(context).textTheme.titleSmall!.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                  ListTile(
                    title: Text(
                      trans("Profile"),
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium!
                          .copyWith(fontSize: 16),
                    ),
                    leading: Icon(Icons.account_circle),
                    onTap: _actionProfile,
                  ),
                    ListTile(
                      title: Text(
                        trans("Wishlist"),
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(fontSize: 16),
                      ),
                      leading: Icon(Icons.favorite_border),
                      onTap: _actionWishlist,
                    ),
                  ListTile(
                    title: Text(
                      trans("Cart"),
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium!
                          .copyWith(fontSize: 16),
                    ),
                    leading: Icon(Icons.shopping_cart),
                    onTap: _actionCart,
                  ),
                ],
              ),
            if (widget.productCategories.isNotEmpty)
              Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 16, top: 8, bottom: 8),
                      child: Text(
                        trans("Categories".tr()),
                        style: Theme.of(context).textTheme.titleSmall!.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    ...widget.productCategories.map((collection) {
                      return ListTile(
                        title: Text(
                          collection.name,
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium!
                              .copyWith(fontSize: 16),
                        ),
                        trailing: Icon(Icons.keyboard_arrow_right_rounded),
                        onTap: () {
                          routeTo(BrowseCategoryPage.path, data: collection);
                        },
                      );
                    })
                  ]),
            // Privacy Policy Link
              if (AppHelper.instance.appConfig?.privacyUrl != null)
                ListTile(
                  title: Text(
                    trans("Privacy policy"),
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium!
                        .copyWith(fontSize: 16),
                  ),
                  trailing: Icon(Icons.keyboard_arrow_right_rounded),
                  leading: Icon(Icons.account_balance),
                  onTap: () => _openUrl(AppHelper.instance.appConfig!.privacyUrl!),
                ),

              // Terms and Conditions Link
              if (AppHelper.instance.appConfig?.termsUrl != null)
                ListTile(
                  title: Text(
                    trans("Terms & Conditions"),
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium!
                        .copyWith(fontSize: 16),
                  ),
                  trailing: Icon(Icons.keyboard_arrow_right_rounded),
                  leading: Icon(Icons.gavel),
                  onTap: () => _openUrl(AppHelper.instance.appConfig!.termsUrl!),
                ),
            ListTile(
              title: Text(trans((isDark ? "Light Mode" : "Dark Mode")),
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium!
                      .copyWith(fontSize: 16)),
              leading: Icon(Icons.brightness_4_rounded),
              onTap: () {
                setState(() {
                  NyTheme.set(context,
                      id: isDark
                          ? "default_light_theme"
                          : "default_dark_theme");
                });
              },
            ),
            if (_menuLinks.isNotEmpty)
              Padding(
                padding: EdgeInsets.only(left: 16, top: 8, bottom: 8),
                child: Text(
                  trans("Social"),
                  style: Theme.of(context).textTheme.titleSmall,
                ),
              ),
            // Dynamic Menu Links from AppConfig
            ..._menuLinks.map((menuLink) => ListTile(
              title: Text(menuLink.title),
              leading: _getIconForMenuLink(menuLink.icon),
              trailing: Icon(Icons.keyboard_arrow_right_rounded),
              onTap: () => _handleMenuLinkTap(menuLink),
            )).toList(),

            ListTile(
              title: Text("Change language".tr()),
              leading: Icon(Icons.language),
              onTap: () {
                NyLanguageSwitcher.showBottomModal(context);
              },
            ),
            ListTile(
              title: AppVersionWidget(),
            ),
          ],
        ),
      ),
    );
  }

  /// Open URL in browser
  Future<void> _openUrl(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        showToastNotification(
          context,
          title: trans("Error"),
          description: trans("Could not open link"),
          style: ToastNotificationStyleType.danger,
        );
      }
    } catch (e) {
      print('Error opening URL: $e');
      showToastNotification(
        context,
        title: trans("Error"),
        description: trans("Could not open link"),
        style: ToastNotificationStyleType.danger,
      );
    }
  }

  /// Handle menu link tap
  void _handleMenuLinkTap(MenuLink menuLink) {
    Navigator.pop(context); // Close drawer first

    if (menuLink.url != null) {
      _openUrl(menuLink.url!);
    } else if (menuLink.routeName != null) {
      // Handle route navigation if needed
      routeTo(menuLink.routeName!);
    }
  }

  /// Get icon for menu link
  Widget _getIconForMenuLink(String? iconName) {
    switch (iconName?.toLowerCase()) {
      case 'info':
        return Icon(Icons.info_outline);
      case 'contact_support':
        return Icon(Icons.contact_support);
      case 'straighten':
        return Icon(Icons.straighten);
      case 'local_shipping':
        return Icon(Icons.local_shipping);
      case 'help':
        return Icon(Icons.help_outline);
      case 'support':
        return Icon(Icons.support_agent);
      case 'phone':
        return Icon(Icons.phone);
      case 'email':
        return Icon(Icons.email);
      default:
        return Icon(Icons.link);
    }
  }

  _actionProfile() async {
    Navigator.pop(context);
    if (!(await AuthService().isLoggedIn())) {
      UserAuth.instance.redirect = AccountDetailPage.path.name;
      routeTo(AccountLoginPage.path);
      return;
    }
    routeTo(AccountDetailPage.path);
  }

  /// Wishlist action
  _actionWishlist() async {
    Navigator.pop(context);
    routeTo(WishListPageWidget.path);
  }

  /// Cart action
  _actionCart() {
    Navigator.pop(context);
    routeTo(CartPage.path);
  }
}
