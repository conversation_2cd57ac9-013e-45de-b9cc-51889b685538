//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import '/resources/pages/checkout_status_page.dart';
import '/bootstrap/data/order_wc.dart';

import '/resources/pages/checkout_confirmation_page.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '../../services/woocommerce_service.dart';


cashOnDeliveryPay(context) async {
  try {
    print('🌐 ===== CASH ON DELIVERY CHECKOUT STARTED =====');

    WooOrder orderWC = await buildOrderWC(markPaid: false);

    // LOG ORDER DETAILS BEFORE SUBMISSION
    print('🌐 ===== ORDER CREATION REQUEST =====');
    print('📍 URL: https://velvete.ly/wp-json/wc/v3/orders');
    print('🔧 METHOD: POST');
    print('📦 ORDER DATA:');
    print('   Payment Method: ${orderWC.paymentMethod}');
    print('   Payment Method Title: ${orderWC.paymentMethodTitle}');
    print('   Status: ${orderWC.status}');
    print('   Set Paid: ${orderWC.setPaid}');
    print('   Customer Note: ${orderWC.customerNote}');
    print('   Line Items Count: ${orderWC.lineItems?.length ?? 0}');
    if (orderWC.lineItems != null) {
      for (int i = 0; i < orderWC.lineItems!.length; i++) {
        var item = orderWC.lineItems![i];
        print('     Item $i: ${item.name} (ID: ${item.productId}, Qty: ${item.quantity}, Total: ${item.total})');
      }
    }
    print('==================================');

    // MASTER'S DEBUG: Log shipping details before order creation
    print('🔍 ===== SHIPPING DETAILS BEFORE ORDER CREATION =====');
    if (orderWC.shippingLines != null && orderWC.shippingLines!.isNotEmpty) {
      print('✅ Shipping Lines Present:');
      for (int i = 0; i < orderWC.shippingLines!.length; i++) {
        var shippingLine = orderWC.shippingLines![i];
        print('   Line $i:');
        print('     Method ID: ${shippingLine.methodId}');
        print('     Method Title: ${shippingLine.methodTitle}');
        print('     Total: ${shippingLine.total}');
      }
    } else {
      print('❌ NO SHIPPING LINES - This will cause "Shipping is required" error!');
    }

    if (orderWC.shipping != null) {
      print('✅ Shipping Address Present:');
      print('   Name: ${orderWC.shipping!.firstName} ${orderWC.shipping!.lastName}');
      print('   Address: ${orderWC.shipping!.address1}');
      print('   City: ${orderWC.shipping!.city}');
      print('   Country: ${orderWC.shipping!.country}');
    } else {
      print('❌ NO SHIPPING ADDRESS');
    }
    print('================================================');

    WooOrder? order = await WooCommerceService().createOrder(orderWC);

    // LOG ORDER CREATION RESPONSE
    print('🌐 ===== ORDER CREATION RESPONSE =====');
    if (order.id != null) {
      print('✅ SUCCESS - Order created');
      print('📊 Order ID: ${order.id}');
      print('📧 Order Number: ${order.number}');
      print('💰 Order Total: ${order.total}');
      print('📋 Order Status: ${order.status}');
    } else {
      print('❌ FAILED - Order creation returned null ID');
      print('📦 Full Order Object: $order');
    }
    print('===================================');

    if (order.id == null) {
      print('❌ CHECKOUT FAILED: Order ID is null');
      showToastNotification(
        context,
        title: trans("Error"),
        description: trans("Something went wrong, please contact our store"),
      );
      updateState(CheckoutConfirmationPage.path.nyPageName(),
          data: {"reloadState": false});
      return;
    }

    print('✅ CHECKOUT SUCCESS: Navigating to status page');
    routeTo(CheckoutStatusPage.path, data: order);
  } catch (e, stackTrace) {
    print('❌ CHECKOUT EXCEPTION: $e');
    print('📋 Stack Trace: $stackTrace');
    showToastNotification(
      context,
      title: trans("Error"),
      description: trans("Something went wrong, please contact our store"),
    );
    updateState(CheckoutConfirmationPage.path.nyPageName(),
        data: {"reloadState": false});
  }
}
