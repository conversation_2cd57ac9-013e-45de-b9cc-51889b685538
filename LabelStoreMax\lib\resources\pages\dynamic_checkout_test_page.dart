//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/services/dynamic_checkout_service.dart';
import '/resources/widgets/safearea_widget.dart';

class DynamicCheckoutTestPage extends NyStatefulWidget {
  static RouteView path = ("/dynamic-checkout-test", (_) => DynamicCheckoutTestPage());

  DynamicCheckoutTestPage({super.key})
      : super(child: () => _DynamicCheckoutTestPageState());
}

class _DynamicCheckoutTestPageState extends NyState<DynamicCheckoutTestPage> {
  final DynamicCheckoutService _dynamicService = DynamicCheckoutService();
  Map<String, dynamic>? _testResults;
  bool _isLoading = false;

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Dynamic Checkout API Test"),
        centerTitle: true,
      ),
      body: SafeAreaWidget(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                "WooCommerce Dynamic Integration Test",
                style: Theme.of(context).textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 20),
              
              ElevatedButton(
                onPressed: _isLoading ? null : _runTests,
                child: _isLoading 
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 10),
                          Text("Running Tests..."),
                        ],
                      )
                    : Text("Run Comprehensive API Tests"),
              ),
              
              SizedBox(height: 20),
              
              if (_testResults != null) ...[
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSummaryCard(),
                        SizedBox(height: 16),
                        _buildPaymentGatewaysCard(),
                        SizedBox(height: 16),
                        _buildShippingMethodsCard(),
                        SizedBox(height: 16),
                        _buildCheckoutFieldsCard(),
                        SizedBox(height: 16),
                        _buildUltimateMemberCard(),
                        SizedBox(height: 16),
                        _buildCustomerAuthCard(),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                Expanded(
                  child: Center(
                    child: Text(
                      "Click the button above to test WooCommerce API endpoints for dynamic checkout integration.",
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _runTests() async {
    setState(() {
      _isLoading = true;
      _testResults = null;
    });

    try {
      final results = await _dynamicService.runComprehensiveTest();
      setState(() {
        _testResults = results;
      });
    } catch (e) {
      print("Error running tests: $e");
      setState(() {
        _testResults = {
          'error': 'Failed to run tests: $e',
        };
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildSummaryCard() {
    final summary = _testResults?['summary'] ?? {};
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Test Summary",
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 12),
            _buildStatusRow("Payment Gateways", summary['payment_gateways_feasible'] ?? false),
            _buildStatusRow("Shipping Methods", summary['shipping_methods_feasible'] ?? false),
            _buildStatusRow("Checkout Fields", summary['checkout_fields_feasible'] ?? false),
            _buildStatusRow("Customer Auth", summary['customer_auth_feasible'] ?? false),
            Divider(),
            _buildStatusRow("Overall Feasibility", summary['overall_feasibility'] ?? false, isOverall: true),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool success, {bool isOverall = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            success ? Icons.check_circle : Icons.error,
            color: success ? Colors.green : Colors.red,
            size: isOverall ? 24 : 20,
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: isOverall 
                  ? Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)
                  : Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Text(
            success ? "FEASIBLE" : "NOT FEASIBLE",
            style: TextStyle(
              color: success ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentGatewaysCard() {
    final paymentData = _testResults?['payment_gateways'] ?? {};
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Payment Gateways API",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 8),
            Text("Endpoint: ${paymentData['endpoint'] ?? 'Unknown'}"),
            Text("Status: ${paymentData['success'] == true ? 'SUCCESS' : 'FAILED'}"),
            if (paymentData['message'] != null)
              Text("Message: ${paymentData['message']}"),
            if (paymentData['error'] != null)
              Text("Error: ${paymentData['error']}", style: TextStyle(color: Colors.red)),
          ],
        ),
      ),
    );
  }

  Widget _buildShippingMethodsCard() {
    final shippingData = _testResults?['shipping_methods'] ?? {};
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Shipping Methods API",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 8),
            Text("Endpoint: ${shippingData['endpoint'] ?? 'Unknown'}"),
            Text("Status: ${shippingData['success'] == true ? 'SUCCESS' : 'FAILED'}"),
            if (shippingData['zones'] != null)
              Text("Zones Found: ${(shippingData['zones'] as List).length}"),
            if (shippingData['methods'] != null)
              Text("Methods Found: ${(shippingData['methods'] as List).length}"),
            if (shippingData['message'] != null)
              Text("Message: ${shippingData['message']}"),
            if (shippingData['error'] != null)
              Text("Error: ${shippingData['error']}", style: TextStyle(color: Colors.red)),
          ],
        ),
      ),
    );
  }

  Widget _buildCheckoutFieldsCard() {
    final checkoutData = _testResults?['checkout_fields'] ?? {};
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Checkout Fields API",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 8),
            Text("Status: ${checkoutData['success'] == true ? 'SUCCESS' : 'FAILED'}"),
            if (checkoutData['test_results'] != null) ...[
              Text("Endpoints Tested: ${(checkoutData['test_results'] as List).length}"),
              for (var result in checkoutData['test_results'])
                Padding(
                  padding: EdgeInsets.only(left: 16, top: 4),
                  child: Text(
                    "${result['endpoint']}: ${result['success'] ? 'SUCCESS' : 'FAILED'}",
                    style: TextStyle(fontSize: 12),
                  ),
                ),
            ],
            if (checkoutData['message'] != null)
              Text("Message: ${checkoutData['message']}"),
          ],
        ),
      ),
    );
  }

  Widget _buildUltimateMemberCard() {
    final umData = _testResults?['ultimate_member'] ?? {};

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Ultimate Member Plugin API",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 8),
            Text("Status: ${umData['success'] == true ? 'SUCCESS' : 'FAILED'}"),
            if (umData['test_results'] != null) ...[
              Text("Endpoints Tested: ${(umData['test_results'] as List).length}"),
              for (var result in umData['test_results'])
                Padding(
                  padding: EdgeInsets.only(left: 16, top: 4),
                  child: Text(
                    "${result['endpoint']}: ${result['success'] ? 'SUCCESS' : 'FAILED'}",
                    style: TextStyle(fontSize: 12),
                  ),
                ),
            ],
            if (umData['note'] != null)
              Text("Note: ${umData['note']}", style: TextStyle(fontStyle: FontStyle.italic)),
            if (umData['message'] != null)
              Text("Message: ${umData['message']}"),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerAuthCard() {
    final authData = _testResults?['customer_auth'] ?? {};

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Customer Authentication API",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 8),
            Text("Status: ${authData['success'] == true ? 'SUCCESS' : 'FAILED'}"),
            if (authData['note'] != null)
              Text("Note: ${authData['note']}", style: TextStyle(fontStyle: FontStyle.italic)),
            if (authData['message'] != null)
              Text("Message: ${authData['message']}"),
          ],
        ),
      ),
    );
  }
}
