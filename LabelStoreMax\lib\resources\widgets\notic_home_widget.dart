//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/widgets/store_logo_widget.dart';
import '/resources/widgets/notification_icon_widget.dart';
import '/resources/widgets/product_item_container_widget.dart';
import '/resources/widgets/no_results_for_products_widget.dart';
import '/resources/pages/product_detail_page.dart';
import '/resources/pages/browse_category_page.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/home_drawer_widget.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/models/app_config.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/models/woocommerce_wrappers/my_product_category.dart';
import '/app/services/woocommerce_service.dart';

class NoticHomeWidget extends StatefulWidget {
  const NoticHomeWidget({super.key, required this.appConfig});

  final AppConfig? appConfig;

  @override
  createState() => _NoticHomeWidgetState();
}

class _NoticHomeWidgetState extends NyState<NoticHomeWidget> {
  Widget? activeWidget;
  List<MyProductCategory> _categories = [];

  @override
  get init => () async {
        await _fetchCategories();
      };

  _fetchCategories() async {
    try {
      // Simplified approach - get all parent categories
      _categories = await WooCommerceService().getProductCategories(
        parent: 0,
        hideEmpty: true,
        perPage: 50,
      );

      // Sort by menu order if available
      _categories.sort((category1, category2) {
        int order1 = category1.getSafeMenuOrder();
        int order2 = category2.getSafeMenuOrder();
        return order1.compareTo(order2);
      });
    } catch (e) {
      NyLogger.error('Error fetching categories: $e');
      _categories = [];
    }
  }

  _modalBottomSheetMenu() {
    wsModalBottom(
      context,
      (BuildContext context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              trans("Categories"),
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 16),
            Flexible(
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: _categories.length,
                separatorBuilder: (cxt, i) => Divider(),
                itemBuilder: (BuildContext context, int index) => ListTile(
                  title: Text(parseHtmlString(_categories[index].name)),
                  onTap: () {
                    Navigator.pop(context);
                    routeTo(BrowseCategoryPage.path, data: _categories[index]);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: HomeDrawerWidget(),
      appBar: AppBar(
        title: StoreLogo(height: 55),
        centerTitle: true,
        actions: [
          Center(
            child: Container(
              margin: EdgeInsets.only(right: 8),
              child: InkWell(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: _modalBottomSheetMenu,
                child: Text(
                  trans("Categories"),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ),
          ),
          Flexible(
              child: Padding(
            padding: EdgeInsets.only(right: 8),
            child: NotificationIcon(),
          )),
        ],
      ),
      body: SafeAreaWidget(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              flex: 1,
              child: afterLoad(
                child: () => ListView(
                  shrinkWrap: true,
                  children: [
                    Container(
                      margin: EdgeInsets.only(bottom: 15),
                      height: MediaQuery.of(context).size.height / 2.5,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          trans("Featured Products"),
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 75,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(trans("Must have")),
                          Flexible(
                            child: Text(
                              trans("Our selection of new items"),
                              style: Theme.of(context).textTheme.headlineMedium,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                        height: 300,
                        width: double.infinity,
                        child: NyPullToRefresh(
                          scrollDirection: Axis.horizontal,
                          child: (context, product) {
                            product as MyProduct;
                            return SizedBox(
                              height: 300,
                              width: 300,
                              child: ProductItemContainer(
                                  product: product,
                                  onTap: () => _showProduct(product)),
                            );
                          },
                          data: (page) {
                            return WooCommerceService().getProducts(
                              perPage: 10,
                              page: page,
                              status: WooFilterStatus.publish,
                              stockStatus: WooProductStockStatus.instock,
                            );
                          },
                          empty: NoResultsForProductsWidget(),
                        ))
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  _showProduct(MyProduct product) =>
      routeTo(ProductDetailPage.path, data: product);
}
