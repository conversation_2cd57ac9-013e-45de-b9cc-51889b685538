//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.


import '/app/services/woocommerce_service.dart';

/// Service to dynamically fetch checkout configuration from WooCommerce API
class DynamicCheckoutService {
  static final DynamicCheckoutService _instance = DynamicCheckoutService._internal();
  factory DynamicCheckoutService() => _instance;
  DynamicCheckoutService._internal();

  final WooCommerceService _wooService = WooCommerceService();

  /// Test and fetch payment gateways from WooCommerce API
  Future<Map<String, dynamic>> testPaymentGateways() async {
    print('=== Testing WooCommerce Payment Gateways API ===');
    
    try {
      // Test payment gateways endpoint
      final response = await _wooService.wooCommerce.dio.get('/payment_gateways');
      
      print('✅ Payment Gateways API Success');
      print('Response type: ${response.data.runtimeType}');
      print('Response length: ${response.data is List ? response.data.length : 'Not a list'}');
      
      if (response.data is List && response.data.isNotEmpty) {
        print('First payment gateway sample:');
        print(response.data[0]);
      }
      
      return {
        'success': true,
        'data': response.data,
        'endpoint': '/payment_gateways',
        'message': 'Payment gateways fetched successfully'
      };
    } catch (e) {
      print('❌ Payment Gateways API Failed: $e');
      return {
        'success': false,
        'error': e.toString(),
        'endpoint': '/payment_gateways',
        'message': 'Failed to fetch payment gateways'
      };
    }
  }

  /// Test and fetch shipping zones and methods from WooCommerce API
  Future<Map<String, dynamic>> testShippingMethods() async {
    print('=== Testing WooCommerce Shipping Methods API ===');
    
    try {
      // Test shipping zones endpoint
      final zonesResponse = await _wooService.wooCommerce.dio.get('/shipping/zones');
      
      print('✅ Shipping Zones API Success');
      print('Zones count: ${zonesResponse.data.length}');
      
      List<Map<String, dynamic>> allMethods = [];
      
      // For each zone, get its methods
      for (var zone in zonesResponse.data) {
        try {
          final methodsResponse = await _wooService.wooCommerce.dio.get('/shipping/zones/${zone['id']}/methods');
          print('Zone ${zone['id']} (${zone['name']}) has ${methodsResponse.data.length} methods');
          
          for (var method in methodsResponse.data) {
            allMethods.add({
              'zone_id': zone['id'],
              'zone_name': zone['name'],
              'method_id': method['id'],
              'method_title': method['method_title'],
              'method_description': method['method_description'],
              'enabled': method['enabled'],
              'settings': method['settings'],
            });
          }
        } catch (e) {
          print('❌ Failed to get methods for zone ${zone['id']}: $e');
        }
      }
      
      return {
        'success': true,
        'zones': zonesResponse.data,
        'methods': allMethods,
        'endpoint': '/shipping/zones and /shipping/zones/{id}/methods',
        'message': 'Shipping methods fetched successfully'
      };
    } catch (e) {
      print('❌ Shipping Methods API Failed: $e');
      return {
        'success': false,
        'error': e.toString(),
        'endpoint': '/shipping/zones',
        'message': 'Failed to fetch shipping methods'
      };
    }
  }

  /// Test checkout fields configuration including custom fields
  Future<Map<String, dynamic>> testCheckoutFields() async {
    print('=== Testing WooCommerce Checkout Fields API ===');

    List<Map<String, dynamic>> testResults = [];

    // Test various endpoints that might contain checkout field info
    List<String> endpointsToTest = [
      '/settings/checkout',
      '/settings/general',
      '/settings/account',
      '/data/countries',
      '/system_status',
    ];

    // Test WordPress REST API for custom fields
    List<String> wpEndpointsToTest = [
      '/wp-json/wp/v2/posts',
      '/wp-json/wp/v2/pages',
      '/wp-json/wp/v2/users',
    ];
    
    for (String endpoint in endpointsToTest) {
      try {
        final response = await _wooService.wooCommerce.dio.get(endpoint);
        testResults.add({
          'endpoint': endpoint,
          'success': true,
          'data_type': response.data.runtimeType.toString(),
          'data_length': response.data is List ? response.data.length : 
                        response.data is Map ? response.data.keys.length : 'unknown',
          'sample_keys': response.data is Map ? response.data.keys.take(5).toList() : null,
        });
        print('✅ $endpoint - Success');
      } catch (e) {
        testResults.add({
          'endpoint': endpoint,
          'success': false,
          'error': e.toString(),
        });
        print('❌ $endpoint - Failed: $e');
      }
    }
    
    // Test WordPress REST API endpoints for custom fields
    for (String endpoint in wpEndpointsToTest) {
      try {
        // Test WordPress REST API endpoints (skip for now due to different base URL)
        // These would need a separate Dio instance with WordPress base URL
        testResults.add({
          'endpoint': endpoint,
          'success': false,
          'error': 'WordPress REST API requires different base URL configuration',
          'note': 'Would need separate Dio instance for WordPress endpoints'
        });
        print('⚠️ $endpoint - Skipped (requires WordPress base URL)');
      } catch (e) {
        testResults.add({
          'endpoint': endpoint,
          'success': false,
          'error': e.toString(),
        });
        print('❌ $endpoint - Failed: $e');
      }
    }

    return {
      'success': testResults.any((result) => result['success'] == true),
      'test_results': testResults,
      'message': 'Checkout fields API test completed',
      'custom_fields_note': 'Custom checkout fields may require specific plugin API or WordPress meta fields'
    };
  }

  /// Test custom checkout fields specifically
  Future<Map<String, dynamic>> testCustomCheckoutFields() async {
    print('=== Testing Custom Checkout Fields Integration ===');

    List<Map<String, dynamic>> testResults = [];

    // Test potential custom field endpoints
    List<String> customFieldEndpoints = [
      '/settings/checkout',
      '/settings',
      '/wp-json/wp/v2/settings',
      '/wp-json/wc/v3/settings/checkout',
    ];

    for (String endpoint in customFieldEndpoints) {
      try {
        final response = await _wooService.wooCommerce.dio.get(endpoint);

        // Look for custom field indicators
        bool hasCustomFields = false;
        List<String> customFieldKeys = [];

        if (response.data is List) {
          for (var item in response.data) {
            if (item is Map && item.containsKey('id')) {
              String id = item['id'].toString();
              if (id.contains('custom') || id.contains('field') || id.contains('checkout')) {
                hasCustomFields = true;
                customFieldKeys.add(id);
              }
            }
          }
        } else if (response.data is Map) {
          for (var key in response.data.keys) {
            String keyStr = key.toString();
            if (keyStr.contains('custom') || keyStr.contains('field') || keyStr.contains('checkout')) {
              hasCustomFields = true;
              customFieldKeys.add(keyStr);
            }
          }
        }

        testResults.add({
          'endpoint': endpoint,
          'success': true,
          'has_custom_fields': hasCustomFields,
          'custom_field_keys': customFieldKeys,
          'data_type': response.data.runtimeType.toString(),
        });
        print('✅ $endpoint - Success (Custom fields: $hasCustomFields)');
      } catch (e) {
        testResults.add({
          'endpoint': endpoint,
          'success': false,
          'error': e.toString(),
        });
        print('❌ $endpoint - Failed: $e');
      }
    }

    return {
      'success': testResults.any((result) => result['success'] == true),
      'test_results': testResults,
      'has_any_custom_fields': testResults.any((result) => result['has_custom_fields'] == true),
      'message': 'Custom checkout fields test completed'
    };
  }

  /// Test Ultimate Member plugin integration
  Future<Map<String, dynamic>> testUltimateMemberIntegration() async {
    print('=== Testing Ultimate Member Plugin Integration ===');

    List<Map<String, dynamic>> testResults = [];

    // Test potential Ultimate Member endpoints
    List<String> umEndpoints = [
      '/wp-json/wp/v2/users',
      '/wp-json/wp/v2/users/me',
      '/wp-json/um/v1/users',
      '/wp-json/um/v1/auth',
      '/wp-json/um/v1/register',
      '/wp-json/um/v1/login',
    ];

    for (String endpoint in umEndpoints) {
      try {
        final response = await _wooService.wooCommerce.dio.get(endpoint);
        testResults.add({
          'endpoint': endpoint,
          'success': true,
          'data_type': response.data.runtimeType.toString(),
          'status_code': response.statusCode,
        });
        print('✅ $endpoint - Success');
      } catch (e) {
        testResults.add({
          'endpoint': endpoint,
          'success': false,
          'error': e.toString(),
        });
        print('❌ $endpoint - Failed: $e');
      }
    }

    return {
      'success': testResults.any((result) => result['success'] == true),
      'test_results': testResults,
      'message': 'Ultimate Member integration test completed',
      'note': 'Ultimate Member may not expose REST API endpoints by default'
    };
  }

  /// Test customer authentication endpoints
  Future<Map<String, dynamic>> testCustomerAuthentication() async {
    print('=== Testing WooCommerce Customer Authentication API ===');
    
    List<Map<String, dynamic>> testResults = [];
    
    // Test various customer-related endpoints
    List<String> endpointsToTest = [
      '/customers',
      '/customers/me', // This might require authentication
    ];
    
    for (String endpoint in endpointsToTest) {
      try {
        final response = await _wooService.wooCommerce.dio.get(endpoint);
        testResults.add({
          'endpoint': endpoint,
          'success': true,
          'data_type': response.data.runtimeType.toString(),
          'data_length': response.data is List ? response.data.length : 
                        response.data is Map ? response.data.keys.length : 'unknown',
        });
        print('✅ $endpoint - Success');
      } catch (e) {
        testResults.add({
          'endpoint': endpoint,
          'success': false,
          'error': e.toString(),
        });
        print('❌ $endpoint - Failed: $e');
      }
    }
    
    return {
      'success': testResults.any((result) => result['success'] == true),
      'test_results': testResults,
      'message': 'Customer authentication API test completed',
      'note': 'WooCommerce uses WordPress authentication, not separate customer auth endpoints'
    };
  }

  /// Run comprehensive test of all dynamic checkout capabilities
  Future<Map<String, dynamic>> runComprehensiveTest() async {
    print('=== Running Comprehensive Dynamic Checkout Test ===');
    
    final results = <String, dynamic>{};
    
    results['payment_gateways'] = await testPaymentGateways();
    results['shipping_methods'] = await testShippingMethods();
    results['checkout_fields'] = await testCheckoutFields();
    results['custom_checkout_fields'] = await testCustomCheckoutFields();

    // Test Ultimate Member integration
    results['ultimate_member'] = await testUltimateMemberIntegration();
    results['customer_auth'] = await testCustomerAuthentication();
    
    // Determine overall feasibility
    bool paymentGatewaysFeasible = results['payment_gateways']['success'] == true;
    bool shippingMethodsFeasible = results['shipping_methods']['success'] == true;
    bool checkoutFieldsFeasible = results['checkout_fields']['success'] == true;
    bool customerAuthFeasible = results['customer_auth']['success'] == true;
    
    results['summary'] = {
      'payment_gateways_feasible': paymentGatewaysFeasible,
      'shipping_methods_feasible': shippingMethodsFeasible,
      'checkout_fields_feasible': checkoutFieldsFeasible,
      'customer_auth_feasible': customerAuthFeasible,
      'overall_feasibility': paymentGatewaysFeasible && shippingMethodsFeasible,
    };
    
    print('=== Test Summary ===');
    print('Payment Gateways: ${paymentGatewaysFeasible ? "✅ FEASIBLE" : "❌ NOT FEASIBLE"}');
    print('Shipping Methods: ${shippingMethodsFeasible ? "✅ FEASIBLE" : "❌ NOT FEASIBLE"}');
    print('Checkout Fields: ${checkoutFieldsFeasible ? "✅ FEASIBLE" : "❌ NOT FEASIBLE"}');
    print('Customer Auth: ${customerAuthFeasible ? "✅ FEASIBLE" : "❌ NOT FEASIBLE"}');
    print('==================');
    
    return results;
  }
}
