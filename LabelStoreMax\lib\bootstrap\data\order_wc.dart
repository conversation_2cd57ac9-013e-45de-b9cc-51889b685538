//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'dart:io';


import '/app/models/cart.dart';
import '/app/models/cart_line_item.dart';
import '/app/models/checkout_session.dart';
import '/bootstrap/helpers.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';


Future<WooOrder> buildOrderWC({bool markPaid = true}) async {
  CheckoutSession checkoutSession = CheckoutSession.getInstance;
  WooOrder orderWC = WooOrder(
    id: 0, // Temporary ID, will be assigned by WooCommerce
  );

  String paymentMethodName = checkoutSession.paymentType!.name;

  orderWC.paymentMethod = Platform.isAndroid
      ? "$paymentMethodName - Android App"
      : "$paymentMethodName - IOS App";

  orderWC.paymentMethodTitle = paymentMethodName.toLowerCase();

  orderWC.setPaid = markPaid;
  orderWC.status = WooOrderStatus.processing;
  // Currency will be handled by WooCommerce backend based on store settings
  // LYD is configured in AppConfig.defaultConfig() currencyMeta
  // Add customer ID when user is authenticated
  WooCustomer? currentUser = await getCurrentUser();
  if (currentUser?.id != null) {
    orderWC.customerId = currentUser!.id!;
    print('🔐 Order assigned to customer ID: ${currentUser.id}');
  } else {
    print('⚠️ Order created as guest (no authenticated user)');
  }

  List<WooLineItem> lineItems = [];
  List<CartLineItem> cartItems = await Cart.getInstance.getCart();
  for (var cartItem in cartItems) {
    WooLineItem lineItem = WooLineItem(
      id: 0, // Will be assigned by WooCommerce
      name: cartItem.name ?? '',
      productId: cartItem.productId ?? 0,
      quantity: cartItem.quantity,
      subtotal: double.tryParse(cartItem.subtotal ?? '0') ?? 0.0,
      total: double.tryParse(cartItem.total ?? '0') ?? 0.0,
    );

    if (cartItem.variationId != null && cartItem.variationId != 0) {
      lineItem.variationId = cartItem.variationId;
    }

    lineItems.add(lineItem);
  }

  orderWC.lineItems = lineItems;

  // Set billing details using WooCommerce API types
  if (checkoutSession.billingDetails?.billingAddress != null) {
    final billingAddress = checkoutSession.billingDetails!.billingAddress!;

    orderWC.billing = WooBilling(
      firstName: billingAddress.firstName,
      lastName: billingAddress.lastName,
      address1: billingAddress.addressLine,
      city: billingAddress.city,
      postcode: billingAddress.postalCode,
      phone: billingAddress.phoneNumber,
      email: billingAddress.emailAddress,
      country: billingAddress.customerCountry?.countryCode ?? 'LY',
    );

    // Set shipping details (same as billing for now)
    orderWC.shipping = WooShipping(
      firstName: billingAddress.firstName,
      lastName: billingAddress.lastName,
      address1: billingAddress.addressLine,
      city: billingAddress.city,
      postcode: billingAddress.postalCode,
      country: billingAddress.customerCountry?.countryCode ?? 'LY',
    );

    print('📋 Order billing/shipping details set');
  } else {
    print('⚠️ No billing details available for order');
  }

  // Add shipping lines when available - CRITICAL FOR WOOCOMMERCE
  if (checkoutSession.shippingType != null) {
    print('🔍 ===== BUILDING SHIPPING LINE FROM CHECKOUT SESSION =====');
    print('📦 CheckoutSession ShippingType:');
    print('   Method ID: ${checkoutSession.shippingType!.methodId}');
    print('   Cost: ${checkoutSession.shippingType!.cost}');
    print('   Object Type: ${checkoutSession.shippingType!.object.runtimeType}');

    Map<String, dynamic>? shippingLineFee = checkoutSession.shippingType!.toShippingLineFee();
    print('📦 Generated Shipping Line Fee: $shippingLineFee');

    if (shippingLineFee != null) {
      // Create WooShippingLine from the shipping type
      WooShippingLine shippingLine = WooShippingLine(
        methodId: shippingLineFee['method_id'] ?? checkoutSession.shippingType!.methodId,
        methodTitle: shippingLineFee['method_title'] ?? 'Shipping',
        total: shippingLineFee['total'] ?? checkoutSession.shippingType!.cost,
      );

      orderWC.shippingLines = [shippingLine];
      print('✅ Successfully created WooShippingLine:');
      print('   Method ID: ${shippingLine.methodId}');
      print('   Method Title: ${shippingLine.methodTitle}');
      print('   Total: ${shippingLine.total}');
    } else {
      print('❌ CRITICAL: Could not create shipping line from shippingType - toShippingLineFee() returned null');
      print('   This will cause "Shipping is required" error in WooCommerce!');
    }
    print('=====================================================');
  } else {
    print('❌ CRITICAL: No shipping type selected - this will cause "Shipping is required" error');
  }
  // Add coupon lines when coupon is applied
  if (checkoutSession.coupon != null) {
    // For now, coupon is handled by WooCommerce automatically when order is created
    // The coupon code will be applied during checkout process
    print('🎫 Order includes coupon: ${checkoutSession.coupon!.code}');
  }

  if (checkoutSession.customerNote?.isNotEmpty ?? false) {
    orderWC.customerNote = checkoutSession.customerNote;
  }

  return orderWC;
}
