//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import '/app/models/cart.dart';
import '/app/models/cart_line_item.dart';
import '/app/models/checkout_session.dart';
import '/app/models/shipping_type.dart';
import '/app/models/libyan_city.dart';
import '/app/services/dynamic_shipping_service.dart';
import '/app/services/libyan_delivery_service.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/app_loader_widget.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'checkout_confirmation_page.dart';
import 'package:nylo_framework/nylo_framework.dart';


class CheckoutShippingTypePage extends NyStatefulWidget {
  static RouteView path =
      ("/checkout-shipping-type", (_) => CheckoutShippingTypePage());

  CheckoutShippingTypePage({super.key})
      : super(child: () => _CheckoutShippingTypePageState());
}

class _CheckoutShippingTypePageState extends NyPage<CheckoutShippingTypePage> {
  bool _isShippingSupported = true, _isLoading = true;
  final List<Map<String, dynamic>> _wsShippingOptions = [];

  // Libya city selection variables
  LibyanCity? _selectedCity;
  List<LibyanCity> _libyanCities = [];
  bool _showCityDropdown = false;


  @override
  get init => () {
        _initializeLibyanCities();
        _getShippingMethods();
      };

  /// Initialize Libyan cities data
  void _initializeLibyanCities() {
    _libyanCities = LibyanCitiesData.getAllCities();
    print('✅ Loaded ${_libyanCities.length} Libyan cities for shipping');
  }

  _getShippingMethods() async {
    print('=== Getting Dynamic Shipping Methods ===');

    try {
      final dynamicShippingService = DynamicShippingService();

      // Get customer address for location-based shipping
      final checkoutSession = CheckoutSession.getInstance;
      String? country = checkoutSession.billingDetails?.shippingAddress?.customerCountry?.name;
      String? state = checkoutSession.billingDetails?.shippingAddress?.addressLine;

      print('Customer location: $country, $state');

      // Fetch dynamic shipping methods from WooCommerce API
      final shippingMethods = await dynamicShippingService.getShippingMethodsForLocation(
        country: country,
        state: state,
      );

      print('✅ Found ${shippingMethods.length} dynamic shipping methods');

      // Implement precise filtering and consolidation
      List<Map<String, dynamic>> finalShippingOptions = [];
      bool vShippingByCityAdded = false; // Flag to ensure "التوصيل (درب السبيل)" is added only once

      // Check if customer is in Libya (now defaults to true for null)
      bool isLibyaCustomer = _isLibyaCustomer(country);

      print('🔍 Filtering shipping methods for ${isLibyaCustomer ? "Libya" : "non-Libya"} customer');

      for (var method in shippingMethods) {
        if (!method.enabled) {
          print('⚠️ Skipped disabled shipping method: ${method.title}');
          continue; // Skip disabled methods
        }

        print('🔍 ===== DETAILED METHOD ANALYSIS =====');
        print('🔍 Processing method: ${method.title}');
        print('🔍 Method ID: ${method.methodId}');
        print('🔍 Instance ID: ${method.instanceId}');
        print('🔍 Zone: ${method.zone.name} (ID: ${method.zone.id})');
        print('🔍 Settings: ${method.settings}');
        print('🔍 Raw Cost: ${method.cost}');
        print('🔍 Cost Type: ${method.cost.runtimeType}');
        print('🔍 =====================================');

        // EXPLICITLY EXCLUDE UNWANTED METHODS (like "الشحن المحلي (حسب المدينة)")
        if (method.title.contains("الشحن المحلي") ||
            method.title.contains("حسب المدينة") ||
            method.zone.name.toLowerCase().contains("zone 0") ||
            method.zone.id == 0) {
          print('⚠️ Skipped unwanted method for display: ${method.title} (Zone: ${method.zone.name})');
          continue; // Skip this method
        }

        // CONSOLIDATE AND ADD "التوصيل (درب السبيل)" ONLY ONCE
        if (method.methodId == "v_shipping_by_city") {
          if (!vShippingByCityAdded) {
            print('🔍 ===== ADDING V_SHIPPING_BY_CITY METHOD =====');
            print('🔍 Original method cost: ${method.cost}');
            print('🔍 Original method settings: ${method.settings}');

            finalShippingOptions.add({
              "object": method, // Keep the original method object for later use
              "title": "التوصيل (درب السبيل)", // Force Arabic title
              "method_id": method.methodId,
              "instance_id": method.instanceId, // Use instance_id to store one of the valid instances
              "zone_id": method.zone.id,
              "cost": method.cost,
            });
            vShippingByCityAdded = true; // Mark as added
            print('✅ Added consolidated Libya shipping method: التوصيل (درب السبيل)');
            print('✅ Final cost in option: ${method.cost}');
          } else {
            print('⚠️ Skipped duplicate v_shipping_by_city method: ${method.title}');
          }
        }
        // If there were other desired shipping methods for Libya, they would be added here
        // For now, based on previous instructions, only v_shipping_by_city is intended.
      }

      // Update the list used for the UI
      _wsShippingOptions.clear();
      _wsShippingOptions.addAll(finalShippingOptions);

      print('📋 Final shipping options count: ${_wsShippingOptions.length}');

      // MASTER'S ORDER: Auto-select single shipping method and lock it
      if (_wsShippingOptions.length == 1) {
        print('🔒 ===== AUTO-SELECTING SINGLE SHIPPING METHOD =====');
        Map<String, dynamic> singleMethod = _wsShippingOptions[0];

        print('🔍 Single method details:');
        print('   Title: ${singleMethod["title"]}');
        print('   Method ID: ${singleMethod["method_id"]}');
        print('   Instance ID: ${singleMethod["instance_id"]}');
        print('   Zone ID: ${singleMethod["zone_id"]}');
        print('   Cost: ${singleMethod["cost"]}');
        print('   Cost Type: ${singleMethod["cost"].runtimeType}');
        print('   Object: ${singleMethod["object"]}');
        print('   Object Type: ${singleMethod["object"].runtimeType}');

        // Create ShippingType and assign to CheckoutSession immediately
        ShippingType autoSelectedShipping = ShippingType(
          methodId: singleMethod['method_id'],
          object: singleMethod['object'],
          cost: singleMethod['cost']?.toString() ?? '0',
          minimumValue: null,
        );

        print('🔍 Created ShippingType object:');
        print('   Method ID: ${autoSelectedShipping.methodId}');
        print('   Cost: ${autoSelectedShipping.cost}');
        print('   Object: ${autoSelectedShipping.object}');

        CheckoutSession.getInstance.shippingType = autoSelectedShipping;

        print('✅ Auto-selected shipping method: ${singleMethod["title"]}');
        print('✅ Method ID: ${singleMethod["method_id"]}');
        print('✅ Cost: ${singleMethod["cost"]}');
        print('✅ CheckoutSession shipping type cost: ${CheckoutSession.getInstance.shippingType?.cost}');
        print('✅ Shipping method is now LOCKED and non-interactive');
        print('================================================');

        // MASTER'S ORDER: Trigger UI refresh for delivery fee display
        setState(() {});

        // Refresh the checkout confirmation page to show updated delivery fee
        StateAction.refreshPage(CheckoutConfirmationPage.path, setState: () {});
      }

      // If no dynamic methods found, add fallback options
      if (_wsShippingOptions.isEmpty) {
        print('⚠️ No dynamic shipping methods found, adding fallback options');
        _addFallbackShippingOptions();
      }

    } catch (e) {
      print("❌ Error fetching dynamic shipping methods: $e");
      print("⚠️ Adding fallback shipping options");
      _addFallbackShippingOptions();
    }

    setState(() {
      _isLoading = false;
    });
  }

  /// Check if customer is in Libya based on country information
  bool _isLibyaCustomer(String? country) {
    // If country is null, assume Libya by default, as all customers should be Libyan.
    if (country == null) return true;

    String countryLower = country.toLowerCase();
    return countryLower.contains('libya') ||
           countryLower.contains('ليبيا') ||
           countryLower == 'ly';
  }

  /// Add fallback shipping options when dynamic fetching fails
  void _addFallbackShippingOptions() {
    final checkoutSession = CheckoutSession.getInstance;
    String? country = checkoutSession.billingDetails?.shippingAddress?.customerCountry?.name;
    bool isLibyaCustomer = _isLibyaCustomer(country);

    print('🔄 Adding fallback shipping options for ${isLibyaCustomer ? "Libya" : "non-Libya"} customer');

    if (isLibyaCustomer) {
      // For Libya customers, only add the Libya-specific method
      Map<String, dynamic> libyaShippingOption = {
        "id": "v_shipping_by_city_fallback",
        "method_id": "v_shipping_by_city",
        "title": "التوصيل (درب السبيل)", // Force Arabic title
        "cost": "0.00", // Will be updated when city is selected
        "object": null
      };
      _wsShippingOptions.add(libyaShippingOption);
      print('✅ Added Libya fallback shipping method: التوصيل (درب السبيل)');
    } else {
      // For non-Libya customers, add standard options
      Map<String, dynamic> flatRateOption = {
        "id": "flat_rate",
        "method_id": "flat_rate",
        "title": "Flat Rate Delivery",
        "cost": "10.00",
        "object": null
      };
      _wsShippingOptions.add(flatRateOption);

      Map<String, dynamic> freeShippingOption = {
        "id": "free_shipping",
        "method_id": "free_shipping",
        "title": "Free Delivery",
        "cost": "0.00",
        "object": null
      };
      _wsShippingOptions.add(freeShippingOption);
      print('✅ Added ${_wsShippingOptions.length} non-Libya fallback shipping options');
    }
  }

  Future<String> _getShippingPrice(int index) async {
    double total = 0;
    List<CartLineItem> cartLineItem = await Cart.getInstance.getCart();

    total += (await (workoutShippingCostWC(
            sum: _wsShippingOptions[index]['cost']))) ??
        0;

    switch (_wsShippingOptions[index]['method_id']) {
      case "flat_rate":
        FlatRate? flatRate = (_wsShippingOptions[index]['object'] as FlatRate?);

        if (cartLineItem.firstWhereOrNull(
                (t) => t.shippingClassId == null || t.shippingClassId == "0") !=
            null) {
          total += await (workoutShippingClassCostWC(
                  sum: flatRate!.classCost,
                  cartLineItem: cartLineItem
                      .where((t) =>
                          t.shippingClassId == null || t.shippingClassId == "0")
                      .toList())) ??
              0;
        }

        List<CartLineItem> cItemsWithShippingClasses = cartLineItem
            .where((t) => t.shippingClassId != null && t.shippingClassId != "0")
            .toList();
        for (int i = 0; i < cItemsWithShippingClasses.length; i++) {
          ShippingClasses? shippingClasses = flatRate!.shippingClasses!
              .firstWhereOrNull(
                  (d) => d.id == cItemsWithShippingClasses[i].shippingClassId);
          if (shippingClasses != null) {
            double classTotal = await (workoutShippingClassCostWC(
                    sum: shippingClasses.cost,
                    cartLineItem: cartLineItem
                        .where((g) => g.shippingClassId == shippingClasses.id)
                        .toList())) ??
                0;
            total += classTotal;
          }
        }
        break;
      case "alsabil_shipping":
        // Al-sabil shipping has a fixed cost already included in total
        break;
      default:
        break;
    }
    return (total).toString();
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: Text(trans("Delivery Methods")),
        automaticallyImplyLeading: false,
        centerTitle: true,
      ),
      body: SafeAreaWidget(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 20),
                child: Center(
                  child: Image.asset(
                    getImageAsset('shipping_icon.png'),
                    height: 100,
                    color: (Theme.of(context).brightness == Brightness.light)
                        ? null
                        : Colors.white,
                    fit: BoxFit.fitHeight,
                    errorBuilder: (context, error, stackTrace) => Icon(Icons.local_shipping, size: 100),
                  ),
                ),
              ),
              Flexible(
                flex: 1,
                child: Container(
                  decoration: BoxDecoration(
                    color: ThemeColor.get(context).backgroundContainer,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow:
                        (Theme.of(context).brightness == Brightness.light)
                            ? wsBoxShadow()
                            : null,
                  ),
                  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      (_isLoading
                          ? SizedBox(
                              height: 200,
                              child: AppLoaderWidget(),
                            )
                          : (_isShippingSupported
                              ? SizedBox(
                                  height: 300,
                                  child: ListView.separated(
                                    itemCount: _wsShippingOptions.length,
                                    separatorBuilder: (context, index) =>
                                        Divider(
                                      color: Colors.black12,
                                    ),
                                    itemBuilder:
                                        (BuildContext context, int index) {
                                      // MASTER'S ORDER: Make single method non-interactive and visually locked
                                      bool isSingleMethodLocked = _wsShippingOptions.length == 1;

                                      return ListTile(
                                        contentPadding: EdgeInsets.only(
                                          left: 16,
                                          right: 16,
                                        ),
                                        title: Row(
                                          children: [
                                            if (isSingleMethodLocked)
                                              Icon(Icons.lock, color: Colors.green, size: 20),
                                            if (isSingleMethodLocked)
                                              SizedBox(width: 8),
                                            Expanded(
                                              child: Text(
                                                _wsShippingOptions[index]['title'],
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .titleMedium!
                                                    .copyWith(
                                                      fontWeight: FontWeight.bold,
                                                      color: isSingleMethodLocked ? Colors.green : null,
                                                    ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        selected: true,
                                        subtitle: NyFutureBuilder<String>(
                                          future: _getShippingPrice(index),
                                          child: (BuildContext context, data) {
                                            Map<String, dynamic>
                                                shippingOption =
                                                _wsShippingOptions[index];
                                            return RichText(
                                              text: TextSpan(
                                                text: '',
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium,
                                                children: <TextSpan>[
                                                  (shippingOption["object"]
                                                          is FreeShipping
                                                      ? TextSpan(
                                                          text: trans(
                                                              "Free postage"),
                                                        )
                                                      : TextSpan(
                                                          text:
                                                              "${trans("Price")}: ${formatStringCurrency(total: data)}",
                                                        )),
                                                  if (shippingOption[
                                                          "min_amount"] !=
                                                      null)
                                                    TextSpan(
                                                        text:
                                                            "\n${trans("Spend a minimum of")} ${formatStringCurrency(total: shippingOption["min_amount"])}",
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodyMedium!
                                                            .copyWith(
                                                                fontSize: 14))
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                        trailing: (CheckoutSession.getInstance
                                                        .shippingType !=
                                                    null &&
                                                CheckoutSession.getInstance
                                                        .shippingType!.object ==
                                                    _wsShippingOptions[index]
                                                        ["object"]
                                            ? Icon(Icons.check, color: Colors.green)
                                            : (isSingleMethodLocked ? Icon(Icons.check_circle, color: Colors.green) : null)),
                                        onTap: isSingleMethodLocked
                                            ? null  // Disable tap for locked method
                                            : () => _handleCheckoutTapped(index),
                                      );
                                    },
                                  ),
                                )
                              : Text(
                                  trans(
                                      "Delivery is not supported for your location, sorry"),
                                  style: Theme.of(context).textTheme.titleLarge,
                                  textAlign: TextAlign.center,
                                ))),

                      // City dropdown for Libya shipping method
                      if (_showCityDropdown) _buildCityDropdown(),
                      LinkButton(
                        title: trans("CANCEL"),
                        action: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _handleCheckoutTapped(int index) async {
    Map<String, dynamic> shippingOptions = _wsShippingOptions[index];

    // Check if this is the Libya shipping method
    if (shippingOptions['method_id'] == 'v_shipping_by_city') {
      // Show city dropdown for Libya shipping
      setState(() {
        _showCityDropdown = true;
      });

      // Create and save the shipping type for v_shipping_by_city
      ShippingType shippingType = ShippingType(
          methodId: shippingOptions['method_id'],
          object: shippingOptions['object'],
          cost: (await _getShippingPrice(index)),
          minimumValue: null);

      if (_wsShippingOptions[index]['min_amount'] != null) {
        shippingType.minimumValue = _wsShippingOptions[index]['min_amount'];
      }

      CheckoutSession.getInstance.shippingType = shippingType;

      // MASTER'S DEBUG: Log complete ShippingType details
      print('🔍 ===== SHIPPING METHOD SELECTION DEBUG =====');
      print('✅ Libya shipping method selected and saved to CheckoutSession');
      print('📦 ShippingType Details:');
      print('   Method ID: ${shippingType.methodId}');
      print('   Cost: ${shippingType.cost}');
      print('   Object Type: ${shippingType.object.runtimeType}');
      if (shippingType.object is CustomShippingMethod) {
        CustomShippingMethod custom = shippingType.object as CustomShippingMethod;
        print('   Custom Method Title: ${custom.title}');
        print('   Custom Method ID: ${custom.methodId}');
      }
      print('   Minimum Value: ${shippingType.minimumValue}');
      print('============================================');

      // Don't pop() here - let user select city first
      return;
    }

    // For other shipping methods, proceed normally
    ShippingType shippingType = ShippingType(
        methodId: shippingOptions['method_id'],
        object: shippingOptions['object'],
        cost: (await _getShippingPrice(index)),
        minimumValue: null);

    if (_wsShippingOptions[index]['min_amount'] != null) {
      shippingType.minimumValue = _wsShippingOptions[index]['min_amount'];
    }

    CheckoutSession.getInstance.shippingType = shippingType;

    pop();
  }

  /// Build city dropdown widget for Libya shipping
  Widget _buildCityDropdown() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر مدينتك', // "Choose your city" in Arabic
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12),
          DropdownButtonFormField<LibyanCity>(
            value: _selectedCity,
            hint: Text('--- اختر مدينتك ---'), // "--- Choose your city ---"
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: _libyanCities.map((LibyanCity city) {
              return DropdownMenuItem<LibyanCity>(
                value: city,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        city.nameArabic,
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                    Text(
                      '${city.deliveryCost.toStringAsFixed(0)} د.ل',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            onChanged: (LibyanCity? newCity) async {
              setState(() {
                _selectedCity = newCity;
              });
              if (newCity != null) {
                await _updateShippingCostForSelectedCity(newCity);
                // Trigger UI update after cost is updated
                setState(() {});
              }
            },
          ),
          if (_selectedCity != null) ...[
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'تكلفة التوصيل إلى ${_selectedCity!.nameArabic}: ${_selectedCity!.deliveryCost.toStringAsFixed(0)} د.ل',
                      style: TextStyle(
                        color: Colors.green[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _confirmLibyaShipping(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text('تأكيد التوصيل'), // "Confirm Delivery"
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _showCityDropdown = false;
                        _selectedCity = null;
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text('إلغاء'), // "Cancel"
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Update shipping cost based on selected Libyan city
  Future<void> _updateShippingCostForSelectedCity(LibyanCity city) async {
    print('=== Updating Shipping Cost for Selected City: ${city.nameArabic} ===');

    final deliveryService = LibyanDeliveryService();
    await deliveryService.updateShippingCostForCity(city.nameArabic);

    print('✅ Updated shipping cost for ${city.nameArabic}');
  }

  /// Confirm Libya shipping with selected city
  void _confirmLibyaShipping() {
    if (_selectedCity == null) return;

    print('=== Confirming Libya Shipping for City: ${_selectedCity!.nameArabic} ===');

    // Create shipping type with city-specific cost
    final deliveryService = LibyanDeliveryService();
    final cityShippingMethod = deliveryService.createLibyanCityShippingMethod(_selectedCity!.nameArabic);

    // Set the shipping type in checkout session
    CheckoutSession.getInstance.shippingType = cityShippingMethod;

    // Update the checkout session's billing and shipping addresses with the selected city
    final checkoutSession = CheckoutSession.getInstance;
    if (checkoutSession.billingDetails?.billingAddress != null) {
      // Update billing address city
      checkoutSession.billingDetails!.billingAddress!.city = _selectedCity!.nameArabic;
      print('✅ Updated billing address city to: ${_selectedCity!.nameArabic}');
    }

    if (checkoutSession.billingDetails?.shippingAddress != null) {
      // Update shipping address city
      checkoutSession.billingDetails!.shippingAddress!.city = _selectedCity!.nameArabic;
      print('✅ Updated shipping address city to: ${_selectedCity!.nameArabic}');
    }

    print('✅ Libya shipping confirmed for ${_selectedCity!.nameArabic}');
    print('💰 Delivery cost: ${_selectedCity!.deliveryCost} LYD');

    // Close the page
    pop();
  }
}
