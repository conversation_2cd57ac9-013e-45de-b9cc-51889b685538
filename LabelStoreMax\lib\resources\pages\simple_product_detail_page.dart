// lib/resources/pages/simple_product_detail_page.dart
// Velvete Store - Simple Product Detail Page
//
// Created by we008.
// 2025, Velvete Store. All rights reserved.

import 'package:flutter/material.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart' as wc_api;
import 'package:nylo_framework/nylo_framework.dart';
import '/app/services/woocommerce_service.dart';
import '/app/models/woocommerce_wrappers/my_product_variation.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/models/cart_line_item.dart';
import '/app/controllers/product_detail_controller.dart';


class SimpleProductDetailPage extends NyStatefulWidget {
  static RouteView path = ("/simple_product_detail", (_) => SimpleProductDetailPage());

  SimpleProductDetailPage({super.key}) : super(child: () => _SimpleProductDetailPageState());
}

class _SimpleProductDetailPageState extends NyPage<SimpleProductDetailPage>
    with SingleTickerProviderStateMixin {
  wc_api.WooProduct? _product;
  TabController? _tabController;
  bool _isLoading = true;

  // Variation-related state
  List<MyProductVariation> _productVariations = [];
  MyProductVariation? _selectedVariation;
  Map<int, dynamic> _selectedAttributes = {};
  ProductDetailController? _controller;

  @override
  get init => () async {
    _tabController = TabController(length: 3, vsync: this);
    _controller = ProductDetailController();
    await _loadProduct();
  };

  Future<void> _loadProduct() async {
    try {
      // Get the product data using Nylo's data method
      final productData = widget.data();

      print("SimpleProductDetailPage received data type: ${productData.runtimeType}");
      print("SimpleProductDetailPage received data: $productData");

      if (productData is wc_api.WooProduct) {
        _product = productData;
      } else if (productData != null) {
        // Try to access the data property using reflection
        try {
          // Check if it's an ArgumentsWrapper and try to get the data
          if (productData.toString().contains('ArgumentsWrapper')) {
            // Try to access the data field using reflection
            final dynamic wrapper = productData;
            print("Wrapper toString: ${wrapper.toString()}");
            print("Wrapper runtimeType: ${wrapper.runtimeType}");

            // Try different ways to access the data
            try {
              final data = wrapper.data;
              if (data is wc_api.WooProduct) {
                _product = data;
              }
            } catch (e1) {
              print("Failed to access .data: $e1");
              try {
                final data = wrapper['data'];
                if (data is wc_api.WooProduct) {
                  _product = data;
                }
              } catch (e2) {
                print("Failed to access ['data']: $e2");
              }
            }
          } else {
            _product = productData as wc_api.WooProduct;
          }
        } catch (e) {
          print("Error casting product: $e");
        }
      }

      if (_product == null) {
        print("Product not found - creating a dummy product for testing");
        // For now, let's create a dummy product to test the UI
        _product = wc_api.WooProduct(
          id: 0,
          name: "Test Product",
          price: 100.0,
          regularPrice: 100.0,
          description: "This is a test product",
          shortDescription: "Test product",
          images: [],
        );
      }

      setState(() {
        _isLoading = false;
      });

      // BLUEPRINT IMPLEMENTATION: Branch based on product type
      if (_product != null) {
        if (_product!.type == wc_api.WooProductType.variable) {
          print("🔄 VARIABLE PRODUCT DETECTED - Implementing full variation logic");
          await _loadProductVariations();
        } else if (_product!.type == wc_api.WooProductType.simple) {
          print("✅ SIMPLE PRODUCT DETECTED - Standard display logic");
          // For simple products, no variation logic needed
        } else {
          print("⚠️ UNKNOWN PRODUCT TYPE: ${_product!.type}");
        }
      }

    } catch (e) {
      print("Error loading product: $e");
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        Navigator.of(context).pop();
      }
    }
  }

  /// Load product variations for variable products
  Future<void> _loadProductVariations() async {
    if (_product?.id == null) return;

    try {
      print('🔄 Loading variations for product ${_product!.id}...');

      final wooCommerceService = WooCommerceService();
      _productVariations = await wooCommerceService.getProductVariations(
        _product!.id!,
        perPage: 100, // Load all variations
      );

      print('✅ Loaded ${_productVariations.length} variations');

      // Initialize attribute selection map
      if (_product!.attributes.isNotEmpty) {
        for (int i = 0; i < _product!.attributes.length; i++) {
          _selectedAttributes[i] = {
            "name": _product!.attributes[i].name,
            "value": null,
          };
        }
      }

      setState(() {}); // Refresh UI to show variation options

    } catch (e) {
      print('❌ Error loading product variations: $e');
    }
  }

  /// Update selected variation based on attribute selection
  void _updateSelectedVariation() {
    if (_controller == null) return;

    _selectedVariation = _controller!.findProductVariation(
      tmpAttributeObj: _selectedAttributes,
      productVariations: _productVariations,
    );

    setState(() {});
  }

  /// Handle attribute selection
  void _selectAttribute(int attributeIndex, String value) {
    _selectedAttributes[attributeIndex]["value"] = value;
    _updateSelectedVariation();
  }

  /// Build variation selection UI
  Widget _buildVariationSelection() {
    if (_product?.attributes.isEmpty == true) {
      return SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Select Options",
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12),

        // Build attribute selectors
        ..._product!.attributes.asMap().entries.map((entry) {
          int index = entry.key;
          var attribute = entry.value;

          return Padding(
            padding: EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  attribute.name ?? "Option",
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: (attribute.options ?? []).map((option) {
                    bool isSelected = _selectedAttributes[index]?["value"] == option;

                    return GestureDetector(
                      onTap: () => _selectAttribute(index, option),
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: isSelected
                                ? Theme.of(context).primaryColor
                                : Colors.grey,
                            width: isSelected ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                          color: isSelected
                              ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                              : Colors.transparent,
                        ),
                        child: Text(
                          option,
                          style: TextStyle(
                            color: isSelected
                                ? Theme.of(context).primaryColor
                                : Colors.black87,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          );
        }).toList(),

        // Show selected variation info
        if (_selectedVariation != null) ...[
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green),
            ),
            child: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    "Price: \$${_selectedVariation!.getSafePrice()}",
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ] else if (_selectedAttributes.values.any((attr) => attr["value"] != null)) ...[
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange),
            ),
            child: Row(
              children: [
                Icon(Icons.warning, color: Colors.orange),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    "This variation is unavailable",
                    style: TextStyle(
                      color: Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Check if product can be added to cart
  bool _canAddToCart() {
    if (_product?.type == wc_api.WooProductType.simple) {
      return true;
    }

    if (_product?.type == wc_api.WooProductType.variable) {
      return _selectedVariation != null;
    }

    return false;
  }

  /// Add product to cart
  void _addToCart() async {
    if (_product == null || _controller == null) return;

    try {
      print('🛒 Adding to cart: ${_product!.name}');

      // Convert WooProduct to MyProduct for cart compatibility
      MyProduct myProduct = MyProduct.fromWooProduct(_product!);

      CartLineItem cartLineItem;

      if (_product!.type == wc_api.WooProductType.variable) {
        // Variable product - must have a selected variation
        if (_selectedVariation == null) {
          showToast(
            title: trans("Oops"),
            description: trans("Please select all product options first"),
            style: ToastNotificationStyleType.warning,
          );
          return;
        }

        // Check if variation is in stock
        if (_selectedVariation!.stockStatus == "outofstock") {
          showToast(
            title: trans("Sorry"),
            description: trans("This variation is out of stock"),
            style: ToastNotificationStyleType.warning,
          );
          return;
        }

        // Build variation options string
        List<String> options = [];
        _selectedAttributes.forEach((key, value) {
          if (value["value"] != null) {
            options.add("${value["name"]}: ${value["value"]}");
          }
        });

        print('🔧 Selected variation: ${_selectedVariation!.id}');
        print('🏷️ Variation options: $options');

        // Create cart line item for variable product
        cartLineItem = CartLineItem.fromProductVariation(
          quantityAmount: 1, // Default quantity
          options: options,
          product: myProduct,
          productVariation: _selectedVariation!,
        );

      } else {
        // Simple product
        if (_product!.stockStatus?.name == "outofstock") {
          showToast(
            title: trans("Sorry"),
            description: trans("This item is out of stock"),
            style: ToastNotificationStyleType.warning,
          );
          return;
        }

        print('📦 Simple product');

        // Create cart line item for simple product
        cartLineItem = CartLineItem.fromProduct(
          quantityAmount: 1, // Default quantity
          product: myProduct,
        );
      }

      // Add to cart using the controller
      await _controller!.itemAddToCart(cartLineItem: cartLineItem);

      print('✅ Successfully added to cart');

    } catch (e) {
      print('❌ Error adding to cart: $e');
      showToast(
        title: trans("Error"),
        description: trans("Failed to add item to cart. Please try again."),
        style: ToastNotificationStyleType.danger,
      );
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget view(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: Text("Product Details"),
          centerTitle: true,
        ),
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final product = _product;
    if (product == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text("Product Details"),
          centerTitle: true,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text("Product not found"),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text("Go Back"),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(product.name ?? "Product Details"),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Name
            Text(
              product.name ?? "Unknown Product",
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),

            // Product Price
            Text(
              "\$${product.price ?? '0.00'}",
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),

            // Variation Selection (only for variable products)
            if (product.type == wc_api.WooProductType.variable) ...[
              _buildVariationSelection(),
              SizedBox(height: 16),
            ],

            // Product Description
            if (product.description?.isNotEmpty == true) ...[
              Text(
                "Description",
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(product.description ?? ""),
              SizedBox(height: 16),
            ],

            // Add to Cart Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _canAddToCart() ? _addToCart : null,
                child: Text("Add to Cart"),
              ),
            ),
          ],
        ),
      ),
    );
  }


}