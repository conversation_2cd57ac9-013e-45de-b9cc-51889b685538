//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/services/auth_service.dart';
import '/resources/widgets/store_logo_widget.dart';
import '/resources/widgets/product_item_container_widget.dart';
import '/resources/pages/browse_category_page.dart';
import '/resources/pages/home_search_page.dart';
import '/resources/pages/product_detail_page.dart';
import '/resources/widgets/cached_image_widget.dart';
import '/resources/widgets/top_nav_widget.dart';
import 'package:flutter_swiper_view/flutter_swiper_view.dart';
import '/resources/widgets/notification_icon_widget.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/cart_icon_widget.dart';
import '/resources/widgets/home_drawer_widget.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/services/woocommerce_service.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/models/woocommerce_wrappers/my_product_category.dart';
import '/app/models/bottom_nav_item.dart';
import '/bootstrap/app_helper.dart';
import '/resources/pages/account_detail_page.dart';
import '/resources/pages/account_login_page.dart';
import '/resources/pages/cart_page.dart';
import '/resources/pages/wishlist_page_widget.dart';
import '/resources/widgets/app_loader_widget.dart';
import '/resources/pages/dynamic_checkout_test_page.dart';

class MelloThemeWidget extends StatefulWidget {
  const MelloThemeWidget({super.key, this.appConfig});
  final dynamic appConfig; // Temporarily dynamic to avoid breaking changes

  @override
  createState() => _MelloThemeWidgetState();
}

class _MelloThemeWidgetState extends NyState<MelloThemeWidget> {
  List<MyProductCategory> _categories = [];
  Widget? activeWidget;
  int _currentIndex = 0;
  List<BottomNavItem> allNavWidgets = [];

  @override
  get init => () async {
        await _fetchCategories();
        await _loadTabs();
      };

  _fetchCategories() async {
    try {
      _categories = await WooCommerceService().getProductCategories(
        parent: 0,
        perPage: 50,
        hideEmpty: true,
      );
      // Sort by name for now (can be customized later)
      _categories.sort((category1, category2) =>
          category1.name.compareTo(category2.name));
    } catch (e) {
      NyLogger.error("Error fetching categories: $e");
      _categories = [];
    }
  }

  _loadTabs() async {
    allNavWidgets = await bottomNavWidgets();
    if (allNavWidgets.isNotEmpty) {
      activeWidget = allNavWidgets[0].tabWidget;
    }
    setState(() {});
  }

  _changeMainWidget(int currentIndex, List<BottomNavItem> allNavWidgets) {
    setState(() {
      _currentIndex = currentIndex;
      activeWidget = allNavWidgets[currentIndex].tabWidget;
    });
  }

  Future<List<BottomNavItem>> bottomNavWidgets() async {
    List<BottomNavItem> items = [];
    items.add(
      BottomNavItem(
          id: 1,
          bottomNavigationBarItem: BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home'.tr(),
          ),
          tabWidget: _buildHomeWidget()),
    );

    items.add(
      BottomNavItem(
          id: 2,
          bottomNavigationBarItem: BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: 'Search'.tr(),
          ),
          tabWidget: HomeSearchPage()),
    );

    if (AppHelper.instance.appConfig!.wishlistEnabled == true) {
      items.add(BottomNavItem(
        id: 3,
        bottomNavigationBarItem: BottomNavigationBarItem(
          icon: Icon(Icons.favorite_border),
          label: 'Wishlist'.tr(),
        ),
        tabWidget: WishListPageWidget(),
      ));
    }

    items.add(BottomNavItem(
      id: 4,
      bottomNavigationBarItem: BottomNavigationBarItem(
          icon: Icon(Icons.shopping_cart), label: 'Cart'.tr()),
      tabWidget: CartPage(),
    ));

    if (AppHelper.instance.appConfig?.wpLoginEnabled == 1) {
      // Check if user is logged in using AuthService
      bool isLoggedIn = await AuthService().isLoggedIn();

      items.add(BottomNavItem(
        id: 5,
        bottomNavigationBarItem: BottomNavigationBarItem(
            icon: Icon(Icons.person), label: 'Account'.tr()),
        tabWidget: isLoggedIn
            ? AccountDetailPage(showLeadingBackButton: false)
            : AccountLoginPage(
                showBackButton: false,
              ),
      ));
    }

    return items;
  }

  Widget _buildHomeWidget() {
    // Load banner images from AppConfig
    List<String>? bannerImages = AppHelper.instance.appConfig?.bannerImages ?? [];
    return Scaffold(
      drawer: HomeDrawerWidget(
        productCategories: _categories,
      ),
      appBar: AppBar(
        title: StoreLogo(height: 55),
        centerTitle: true,
        actions: [
          IconButton(
            alignment: Alignment.centerLeft,
            icon: Icon(
              Icons.search,
              size: 35,
            ),
            onPressed: () {
              routeTo(HomeSearchPage.path);
            },
          ),
          Flexible(child: NotificationIcon()),
          CartIconWidget(),
        ],
      ),
      body: SafeAreaWidget(
        child: NyPullToRefresh.grid(
          header: Column(
            children: [
              // UPDATED TEMPORARY UI DEBUGGING: CONFIRM PRODUCTS REACH UI AND THEIR COUNT
              SizedBox(
                height: 30,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  alignment: Alignment.centerLeft,
                  child: NyFutureBuilder<List<MyProduct>>(
                    future: WooCommerceService().getProducts(perPage: 5),
                    child: (BuildContext context, products) {
                      return Text(
                        'DEBUG UI: Products for grid: ${products?.length ?? 0} - First: ${products?.isNotEmpty == true ? products!.first.name : "N/A"}',
                        style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.purple),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      );
                    },
                  ),
                ),
              ),
              if (bannerImages.isNotEmpty)
                SizedBox(
                  height: 300,
                  child: Swiper(
                    itemBuilder: (BuildContext context, int index) {
                      return CachedImageWidget(
                        image: bannerImages[index],
                        fit: BoxFit.contain,
                      );
                    },
                    itemCount: bannerImages.length,
                    viewportFraction: 0.8,
                    scale: 0.9,
                  ),
                ),
              TopNavWidget(onPressBrowseCategories: _modalBottomSheetMenu),
            ],
          ),
          child: (context, product) {
            product as MyProduct;
            return SizedBox(
              height: 300,
              child: ProductItemContainer(
                product: product,
                onTap: () => _showProduct(product),
              ),
            );
          },
          data: (page) async {
            try {
              return await WooCommerceService().getProducts(
                page: page,
                perPage: 50,
                status: WooFilterStatus.publish,
                stockStatus: WooProductStockStatus.instock,
              );
            } catch (e) {
              NyLogger.error("Error fetching products: $e");
              return <MyProduct>[];
            }
          },
        ),
      ),
    );
  }

  _modalBottomSheetMenu() {
    wsModalBottom(
      context,
      (BuildContext context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              trans("Categories"),
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 16),
            Flexible(
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: _categories.length,
                separatorBuilder: (cxt, i) => Divider(),
                itemBuilder: (BuildContext context, int index) => ListTile(
                  title: Text(parseHtmlString(_categories[index].name)),
                  onTap: () {
                    Navigator.pop(context);
                    routeTo(BrowseCategoryPage.path, data: _categories[index]);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      body: activeWidget ?? AppLoaderWidget(),
      resizeToAvoidBottomInset: false,
      floatingActionButton: getEnv('APP_DEBUG', defaultValue: false) == true
          ? FloatingActionButton(
              onPressed: () => routeTo(DynamicCheckoutTestPage.path),
              child: Icon(Icons.api),
              tooltip: "API Test",
            )
          : null,
      bottomNavigationBar: allNavWidgets.isEmpty
          ? AppLoaderWidget()
          : BottomNavigationBar(
              backgroundColor: Colors.red, // TEMPORARY: Force visibility
              onTap: (currentIndex) =>
                  _changeMainWidget(currentIndex, allNavWidgets),
              currentIndex: _currentIndex,
              unselectedItemColor: Colors.black54,
              type: BottomNavigationBarType.fixed,
              fixedColor: Colors.black87,
              selectedLabelStyle: TextStyle(color: Colors.black),
              unselectedLabelStyle: TextStyle(
                color: Colors.black87,
              ),
              showSelectedLabels: true,
              showUnselectedLabels: true,
              items:
                  allNavWidgets.map((e) => e.bottomNavigationBarItem).toList(),
            ),
    );
  }

  _showProduct(MyProduct product) {
    routeTo(ProductDetailPage.path, data: product);
  }
}
